import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Alert } from 'react-native'
import { AppwriteService } from '@hvppyplug/mobile-services'

export interface VendorProfile {
  id: string
  email: string
  name: string
  businessName: string
  businessType: 'individual' | 'company'
  phone?: string
  avatar?: string
  isVerified: boolean
  verificationStatus: 'pending' | 'verified' | 'rejected'
  businessAddress: {
    street: string
    city: string
    province: string
    postalCode: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  businessInfo: {
    description: string
    website?: string
    socialMedia?: {
      facebook?: string
      instagram?: string
      twitter?: string
    }
    registrationNumber?: string
    taxNumber?: string
  }
  serviceAreas: string[]
  operatingHours: {
    [key: string]: {
      isOpen: boolean
      openTime: string
      closeTime: string
    }
  }
  bankDetails?: {
    accountHolder: string
    bankName: string
    accountNumber: string
    branchCode: string
  }
  documents: {
    id: string
    type: 'identity' | 'business_registration' | 'tax_certificate' | 'bank_statement'
    url: string
    status: 'pending' | 'approved' | 'rejected'
    uploadedAt: string
  }[]
  rating: {
    average: number
    count: number
  }
  stats: {
    totalOrders: number
    completedOrders: number
    totalEarnings: number
    joinedDate: string
  }
  preferences: {
    notifications: {
      newOrders: boolean
      orderUpdates: boolean
      messages: boolean
      marketing: boolean
    }
    autoAcceptOrders: boolean
    maxOrdersPerDay?: number
  }
  createdAt: string
  updatedAt: string
}

interface AuthState {
  // State
  user: VendorProfile | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (data: {
    email: string
    password: string
    name: string
    businessName: string
    businessType: 'individual' | 'company'
    phone: string
    businessAddress: VendorProfile['businessAddress']
  }) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (updates: Partial<VendorProfile>) => Promise<void>
  uploadDocument: (type: VendorProfile['documents'][0]['type'], file: any) => Promise<void>
  refreshProfile: () => Promise<void>
  clearError: () => void
  
  // Verification
  requestVerification: () => Promise<void>
  updateBankDetails: (bankDetails: VendorProfile['bankDetails']) => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null })

          // For demo purposes, create a mock vendor user
          if (email === '<EMAIL>' && password === 'password123') {
            const mockUser: VendorProfile = {
              id: 'vendor_123',
              email: '<EMAIL>',
              name: 'John Smith',
              businessName: 'John\'s Home Services',
              businessType: 'individual',
              phone: '+27 82 123 4567',
              avatar: undefined,
              isVerified: true,
              verificationStatus: 'verified',
              businessAddress: {
                street: '123 Main Street',
                city: 'Johannesburg',
                province: 'Gauteng',
                postalCode: '2001',
                coordinates: {
                  latitude: -26.2041,
                  longitude: 28.0473
                }
              },
              businessInfo: {
                description: 'Professional home services including plumbing, electrical, and general maintenance.',
                website: 'https://johnshomeservices.co.za',
                socialMedia: {
                  facebook: 'johnshomeservices',
                  instagram: '@johnshomeservices'
                }
              },
              serviceAreas: ['Johannesburg', 'Sandton', 'Randburg'],
              operatingHours: {
                monday: { isOpen: true, openTime: '08:00', closeTime: '17:00' },
                tuesday: { isOpen: true, openTime: '08:00', closeTime: '17:00' },
                wednesday: { isOpen: true, openTime: '08:00', closeTime: '17:00' },
                thursday: { isOpen: true, openTime: '08:00', closeTime: '17:00' },
                friday: { isOpen: true, openTime: '08:00', closeTime: '17:00' },
                saturday: { isOpen: true, openTime: '09:00', closeTime: '15:00' },
                sunday: { isOpen: false, openTime: '', closeTime: '' },
              },
              bankDetails: {
                accountHolder: 'John Smith',
                bankName: 'Standard Bank',
                accountNumber: '*********',
                branchCode: '051001'
              },
              documents: [
                {
                  id: 'doc_1',
                  type: 'identity',
                  url: 'https://example.com/id.pdf',
                  status: 'approved',
                  uploadedAt: new Date().toISOString()
                }
              ],
              rating: {
                average: 4.8,
                count: 127
              },
              stats: {
                totalOrders: 245,
                completedOrders: 238,
                totalEarnings: 45600,
                joinedDate: '2023-01-15T00:00:00.000Z'
              },
              preferences: {
                notifications: {
                  newOrders: true,
                  orderUpdates: true,
                  messages: true,
                  marketing: false
                },
                autoAcceptOrders: false,
                maxOrdersPerDay: 8
              },
              createdAt: '2023-01-15T00:00:00.000Z',
              updatedAt: new Date().toISOString()
            }

            set({
              user: mockUser,
              isAuthenticated: true,
              isLoading: false
            })
            return
          }

          // For production, use real Appwrite authentication
          // const appwriteService = AppwriteService.getInstance()
          // await appwriteService.account.createEmailSession(email, password)
          // ... rest of real authentication logic

          throw new Error('Invalid credentials. Use <EMAIL> / password123 for demo.')


        } catch (error: any) {
          console.error('Login failed:', error)
          set({ 
            error: error.message || 'Login failed', 
            isLoading: false 
          })
          throw error
        }
      },

      register: async (data) => {
        try {
          set({ isLoading: true, error: null })

          // For demo purposes, just show success message
          Alert.alert('Registration', 'Registration feature coming soon! Use <EMAIL> / password123 to login.')
          set({ isLoading: false })
        } catch (error: any) {
          console.error('Registration failed:', error)
          set({
            error: error.message || 'Registration failed',
            isLoading: false
          })
          throw error
        }
      },

      logout: async () => {
        try {
          // For demo purposes, just clear local state
          set({
            user: null,
            isAuthenticated: false,
            error: null
          })
        } catch (error) {
          console.error('Logout error:', error)
        }
      },

      updateProfile: async (updates) => {
        try {
          set({ isLoading: true, error: null })
          
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          const appwriteService = AppwriteService.getInstance()
          
          // Update vendor profile in database
          await appwriteService.databases.updateDocument(
            'hvppyplug-main',
            'vendors',
            user.id,
            updates
          )

          // Update local state
          set({ 
            user: { ...user, ...updates },
            isLoading: false 
          })
        } catch (error: any) {
          console.error('Profile update failed:', error)
          set({ 
            error: error.message || 'Profile update failed', 
            isLoading: false 
          })
          throw error
        }
      },

      uploadDocument: async (type, file) => {
        try {
          set({ isLoading: true, error: null })
          
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          const appwriteService = AppwriteService.getInstance()
          
          // Upload file to storage
          const uploadedFile = await appwriteService.storage.createFile(
            'documents',
            'unique()',
            file
          )

          // Get file URL
          const fileUrl = appwriteService.storage.getFileView('documents', uploadedFile.$id)

          // Add document to user profile
          const newDocument = {
            id: uploadedFile.$id,
            type,
            url: fileUrl.href,
            status: 'pending' as const,
            uploadedAt: new Date().toISOString(),
          }

          const updatedDocuments = [...user.documents, newDocument]
          
          await get().updateProfile({ documents: updatedDocuments })
        } catch (error: any) {
          console.error('Document upload failed:', error)
          set({ 
            error: error.message || 'Document upload failed', 
            isLoading: false 
          })
          throw error
        }
      },

      refreshProfile: async () => {
        try {
          const { user } = get()
          if (!user) return

          const appwriteService = AppwriteService.getInstance()
          
          // Get updated vendor profile
          const vendorProfile = await appwriteService.databases.getDocument(
            'hvppyplug-main',
            'vendors',
            user.id
          )

          const updatedUser: VendorProfile = {
            ...user,
            ...vendorProfile,
            updatedAt: vendorProfile.$updatedAt,
          }

          set({ user: updatedUser })
        } catch (error: any) {
          console.error('Profile refresh failed:', error)
          set({ error: error.message || 'Profile refresh failed' })
        }
      },

      requestVerification: async () => {
        try {
          set({ isLoading: true, error: null })
          
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          // Check if all required documents are uploaded
          const requiredDocs = ['identity', 'business_registration']
          const uploadedDocTypes = user.documents.map(doc => doc.type)
          const missingDocs = requiredDocs.filter(type => !uploadedDocTypes.includes(type))

          if (missingDocs.length > 0) {
            throw new Error(`Missing required documents: ${missingDocs.join(', ')}`)
          }

          await get().updateProfile({ verificationStatus: 'pending' })
          
          set({ isLoading: false })
        } catch (error: any) {
          console.error('Verification request failed:', error)
          set({ 
            error: error.message || 'Verification request failed', 
            isLoading: false 
          })
          throw error
        }
      },

      updateBankDetails: async (bankDetails) => {
        await get().updateProfile({ bankDetails })
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'hvppyplug-vendor-auth',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
