import { create } from 'zustand'
import { AppwriteService } from '@hvppyplug/mobile-services'

export interface VendorOrder {
  id: string
  customerId: string
  customerName: string
  customerPhone: string
  customerAvatar?: string
  serviceId: string
  serviceName: string
  serviceCategory: string
  description: string
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled' | 'declined'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // Pricing
  basePrice: number
  additionalCharges: {
    name: string
    amount: number
    description?: string
  }[]
  totalAmount: number
  paymentStatus: 'pending' | 'paid' | 'refunded'
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'mobile_money'
  
  // Scheduling
  scheduledDate: string
  scheduledTime: string
  estimatedDuration: number // in minutes
  actualStartTime?: string
  actualEndTime?: string
  
  // Location
  serviceAddress: {
    street: string
    city: string
    province: string
    postalCode: string
    coordinates: {
      latitude: number
      longitude: number
    }
    instructions?: string
  }
  
  // Communication
  messages: {
    id: string
    senderId: string
    senderName: string
    message: string
    timestamp: string
    type: 'text' | 'image' | 'file'
    attachments?: {
      url: string
      type: string
      name: string
    }[]
  }[]
  
  // Progress tracking
  progressSteps: {
    id: string
    title: string
    description: string
    status: 'pending' | 'in_progress' | 'completed'
    timestamp?: string
    images?: string[]
    notes?: string
  }[]
  
  // Reviews and ratings
  customerRating?: {
    rating: number
    review: string
    timestamp: string
  }
  vendorNotes?: string
  
  // Metadata
  createdAt: string
  updatedAt: string
  acceptedAt?: string
  completedAt?: string
  cancelledAt?: string
  cancellationReason?: string
}

interface OrdersState {
  // State
  orders: VendorOrder[]
  pendingOrders: VendorOrder[]
  activeOrders: VendorOrder[]
  completedOrders: VendorOrder[]
  isLoading: boolean
  error: string | null
  
  // Filters and sorting
  statusFilter: string
  dateFilter: 'today' | 'week' | 'month' | 'all'
  sortBy: 'date' | 'priority' | 'amount'
  sortOrder: 'asc' | 'desc'

  // Actions
  fetchOrders: () => Promise<void>
  acceptOrder: (orderId: string) => Promise<void>
  declineOrder: (orderId: string, reason: string) => Promise<void>
  updateOrderStatus: (orderId: string, status: VendorOrder['status']) => Promise<void>
  startOrder: (orderId: string) => Promise<void>
  completeOrder: (orderId: string, notes?: string) => Promise<void>
  cancelOrder: (orderId: string, reason: string) => Promise<void>
  updateProgress: (orderId: string, stepId: string, status: 'completed' | 'in_progress', notes?: string, images?: string[]) => Promise<void>
  sendMessage: (orderId: string, message: string, attachments?: any[]) => Promise<void>
  addCharges: (orderId: string, charges: VendorOrder['additionalCharges']) => Promise<void>
  
  // Filters
  setStatusFilter: (status: string) => void
  setDateFilter: (filter: 'today' | 'week' | 'month' | 'all') => void
  setSorting: (sortBy: 'date' | 'priority' | 'amount', order: 'asc' | 'desc') => void
  
  // Utilities
  getOrderById: (orderId: string) => VendorOrder | undefined
  getOrdersByStatus: (status: VendorOrder['status']) => VendorOrder[]
  getTodaysOrders: () => VendorOrder[]
  getOrdersStats: () => {
    total: number
    pending: number
    active: number
    completed: number
    todayEarnings: number
    weekEarnings: number
    monthEarnings: number
  }
  clearError: () => void
}

export const useOrdersStore = create<OrdersState>((set, get) => ({
  // Initial state
  orders: [],
  pendingOrders: [],
  activeOrders: [],
  completedOrders: [],
  isLoading: false,
  error: null,
  statusFilter: 'all',
  dateFilter: 'all',
  sortBy: 'date',
  sortOrder: 'desc',

  // Actions
  fetchOrders: async () => {
    try {
      set({ isLoading: true, error: null })
      
      const appwriteService = AppwriteService.getInstance()
      
      // Get current vendor ID (you'd get this from auth store)
      const account = await appwriteService.account.get()
      
      // Fetch orders for this vendor
      const response = await appwriteService.databases.listDocuments(
        'hvppyplug-main',
        'orders',
        [
          // Query for orders assigned to this vendor
          // appwriteService.Query.equal('vendorId', account.$id),
          // appwriteService.Query.orderDesc('$createdAt'),
        ]
      )

      // Mock data for now
      const mockOrders: VendorOrder[] = [
        {
          id: '1',
          customerId: 'customer_1',
          customerName: 'Sarah Johnson',
          customerPhone: '+27 82 123 4567',
          serviceId: 'service_1',
          serviceName: 'Plumbing Repair',
          serviceCategory: 'Home Maintenance',
          description: 'Fix leaking kitchen tap and check bathroom pipes',
          status: 'pending',
          priority: 'high',
          basePrice: 350,
          additionalCharges: [],
          totalAmount: 350,
          paymentStatus: 'pending',
          paymentMethod: 'card',
          scheduledDate: new Date().toISOString().split('T')[0],
          scheduledTime: '14:00',
          estimatedDuration: 120,
          serviceAddress: {
            street: '123 Main Street',
            city: 'Johannesburg',
            province: 'Gauteng',
            postalCode: '2001',
            coordinates: { latitude: -26.2041, longitude: 28.0473 },
            instructions: 'Ring the doorbell twice'
          },
          messages: [],
          progressSteps: [
            { id: '1', title: 'Assess Problem', description: 'Initial assessment', status: 'pending' },
            { id: '2', title: 'Repair Work', description: 'Fix the issues', status: 'pending' },
            { id: '3', title: 'Quality Check', description: 'Test repairs', status: 'pending' },
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          customerId: 'customer_2',
          customerName: 'Mike Chen',
          customerPhone: '+27 83 987 6543',
          serviceId: 'service_2',
          serviceName: 'Electrical Installation',
          serviceCategory: 'Electrical',
          description: 'Install new ceiling fan in bedroom',
          status: 'in_progress',
          priority: 'medium',
          basePrice: 450,
          additionalCharges: [
            { name: 'Additional wiring', amount: 100, description: 'Extra wiring needed' }
          ],
          totalAmount: 550,
          paymentStatus: 'pending',
          paymentMethod: 'cash',
          scheduledDate: new Date().toISOString().split('T')[0],
          scheduledTime: '10:00',
          estimatedDuration: 180,
          actualStartTime: new Date(Date.now() - 3600000).toISOString(),
          serviceAddress: {
            street: '456 Oak Avenue',
            city: 'Pretoria',
            province: 'Gauteng',
            postalCode: '0001',
            coordinates: { latitude: -25.7479, longitude: 28.2293 },
          },
          messages: [
            {
              id: '1',
              senderId: 'customer_2',
              senderName: 'Mike Chen',
              message: 'I\'ll be home all day, just ring the bell',
              timestamp: new Date(Date.now() - 7200000).toISOString(),
              type: 'text'
            }
          ],
          progressSteps: [
            { id: '1', title: 'Setup', description: 'Prepare tools and materials', status: 'completed', timestamp: new Date(Date.now() - 3000000).toISOString() },
            { id: '2', title: 'Installation', description: 'Install ceiling fan', status: 'in_progress' },
            { id: '3', title: 'Testing', description: 'Test installation', status: 'pending' },
          ],
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          updatedAt: new Date().toISOString(),
          acceptedAt: new Date(Date.now() - 82800000).toISOString(),
        }
      ]

      // Categorize orders
      const pendingOrders = mockOrders.filter(order => order.status === 'pending')
      const activeOrders = mockOrders.filter(order => ['accepted', 'in_progress'].includes(order.status))
      const completedOrders = mockOrders.filter(order => ['completed', 'cancelled'].includes(order.status))

      set({ 
        orders: mockOrders,
        pendingOrders,
        activeOrders,
        completedOrders,
        isLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to fetch orders:', error)
      set({ 
        error: error.message || 'Failed to fetch orders', 
        isLoading: false 
      })
    }
  },

  acceptOrder: async (orderId: string) => {
    try {
      set({ isLoading: true, error: null })
      
      const appwriteService = AppwriteService.getInstance()
      
      // Update order status in database
      await appwriteService.databases.updateDocument(
        'hvppyplug-main',
        'orders',
        orderId,
        { 
          status: 'accepted',
          acceptedAt: new Date().toISOString()
        }
      )

      // Update local state
      const { orders } = get()
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { ...order, status: 'accepted' as const, acceptedAt: new Date().toISOString() }
          : order
      )

      const pendingOrders = updatedOrders.filter(order => order.status === 'pending')
      const activeOrders = updatedOrders.filter(order => ['accepted', 'in_progress'].includes(order.status))

      set({ 
        orders: updatedOrders,
        pendingOrders,
        activeOrders,
        isLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to accept order:', error)
      set({ 
        error: error.message || 'Failed to accept order', 
        isLoading: false 
      })
      throw error
    }
  },

  declineOrder: async (orderId: string, reason: string) => {
    try {
      set({ isLoading: true, error: null })
      
      const appwriteService = AppwriteService.getInstance()
      
      // Update order status in database
      await appwriteService.databases.updateDocument(
        'hvppyplug-main',
        'orders',
        orderId,
        { 
          status: 'declined',
          cancellationReason: reason,
          cancelledAt: new Date().toISOString()
        }
      )

      // Update local state
      const { orders } = get()
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              status: 'declined' as const, 
              cancellationReason: reason,
              cancelledAt: new Date().toISOString()
            }
          : order
      )

      const pendingOrders = updatedOrders.filter(order => order.status === 'pending')

      set({ 
        orders: updatedOrders,
        pendingOrders,
        isLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to decline order:', error)
      set({ 
        error: error.message || 'Failed to decline order', 
        isLoading: false 
      })
      throw error
    }
  },

  updateOrderStatus: async (orderId: string, status: VendorOrder['status']) => {
    try {
      const { orders } = get()
      const updatedOrders = orders.map(order => 
        order.id === orderId 
          ? { ...order, status, updatedAt: new Date().toISOString() }
          : order
      )

      // Recategorize orders
      const pendingOrders = updatedOrders.filter(order => order.status === 'pending')
      const activeOrders = updatedOrders.filter(order => ['accepted', 'in_progress'].includes(order.status))
      const completedOrders = updatedOrders.filter(order => ['completed', 'cancelled'].includes(order.status))

      set({ 
        orders: updatedOrders,
        pendingOrders,
        activeOrders,
        completedOrders
      })
    } catch (error: any) {
      console.error('Failed to update order status:', error)
      set({ error: error.message || 'Failed to update order status' })
    }
  },

  startOrder: async (orderId: string) => {
    await get().updateOrderStatus(orderId, 'in_progress')
    
    // Update actual start time
    const { orders } = get()
    const updatedOrders = orders.map(order => 
      order.id === orderId 
        ? { ...order, actualStartTime: new Date().toISOString() }
        : order
    )
    set({ orders: updatedOrders })
  },

  completeOrder: async (orderId: string, notes?: string) => {
    await get().updateOrderStatus(orderId, 'completed')
    
    // Update completion details
    const { orders } = get()
    const updatedOrders = orders.map(order => 
      order.id === orderId 
        ? { 
            ...order, 
            actualEndTime: new Date().toISOString(),
            completedAt: new Date().toISOString(),
            vendorNotes: notes
          }
        : order
    )
    set({ orders: updatedOrders })
  },

  cancelOrder: async (orderId: string, reason: string) => {
    await get().updateOrderStatus(orderId, 'cancelled')
    
    // Update cancellation details
    const { orders } = get()
    const updatedOrders = orders.map(order => 
      order.id === orderId 
        ? { 
            ...order, 
            cancellationReason: reason,
            cancelledAt: new Date().toISOString()
          }
        : order
    )
    set({ orders: updatedOrders })
  },

  updateProgress: async (orderId: string, stepId: string, status: 'completed' | 'in_progress', notes?: string, images?: string[]) => {
    const { orders } = get()
    const updatedOrders = orders.map(order => {
      if (order.id === orderId) {
        const updatedSteps = order.progressSteps.map(step => 
          step.id === stepId 
            ? { 
                ...step, 
                status, 
                timestamp: new Date().toISOString(),
                notes,
                images 
              }
            : step
        )
        return { ...order, progressSteps: updatedSteps }
      }
      return order
    })
    set({ orders: updatedOrders })
  },

  sendMessage: async (orderId: string, message: string, attachments?: any[]) => {
    const { orders } = get()
    const newMessage = {
      id: Date.now().toString(),
      senderId: 'vendor', // Current vendor ID
      senderName: 'You',
      message,
      timestamp: new Date().toISOString(),
      type: 'text' as const,
      attachments
    }

    const updatedOrders = orders.map(order => {
      if (order.id === orderId) {
        return { 
          ...order, 
          messages: [...order.messages, newMessage]
        }
      }
      return order
    })
    set({ orders: updatedOrders })
  },

  addCharges: async (orderId: string, charges: VendorOrder['additionalCharges']) => {
    const { orders } = get()
    const updatedOrders = orders.map(order => {
      if (order.id === orderId) {
        const newTotal = order.basePrice + charges.reduce((sum, charge) => sum + charge.amount, 0)
        return { 
          ...order, 
          additionalCharges: [...order.additionalCharges, ...charges],
          totalAmount: newTotal
        }
      }
      return order
    })
    set({ orders: updatedOrders })
  },

  // Filters
  setStatusFilter: (status: string) => set({ statusFilter: status }),
  setDateFilter: (filter: 'today' | 'week' | 'month' | 'all') => set({ dateFilter: filter }),
  setSorting: (sortBy: 'date' | 'priority' | 'amount', order: 'asc' | 'desc') => set({ sortBy, sortOrder: order }),

  // Utilities
  getOrderById: (orderId: string) => {
    const { orders } = get()
    return orders.find(order => order.id === orderId)
  },

  getOrdersByStatus: (status: VendorOrder['status']) => {
    const { orders } = get()
    return orders.filter(order => order.status === status)
  },

  getTodaysOrders: () => {
    const { orders } = get()
    const today = new Date().toISOString().split('T')[0]
    return orders.filter(order => order.scheduledDate === today)
  },

  getOrdersStats: () => {
    const { orders } = get()
    const today = new Date().toISOString().split('T')[0]
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()

    return {
      total: orders.length,
      pending: orders.filter(o => o.status === 'pending').length,
      active: orders.filter(o => ['accepted', 'in_progress'].includes(o.status)).length,
      completed: orders.filter(o => o.status === 'completed').length,
      todayEarnings: orders
        .filter(o => o.status === 'completed' && o.scheduledDate === today)
        .reduce((sum, o) => sum + o.totalAmount, 0),
      weekEarnings: orders
        .filter(o => o.status === 'completed' && o.createdAt >= weekAgo)
        .reduce((sum, o) => sum + o.totalAmount, 0),
      monthEarnings: orders
        .filter(o => o.status === 'completed' && o.createdAt >= monthAgo)
        .reduce((sum, o) => sum + o.totalAmount, 0),
    }
  },

  clearError: () => set({ error: null }),
}))
